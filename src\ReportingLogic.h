#ifndef REPORTING_LOGIC_H
#define REPORTING_LOGIC_H

#include <Arduino.h>
#include "Config.h"
#include "SensorDataManager.h"
#include "ConnectivityManager.h"
#include "TDS_Sensor_UART.h"

enum ReportReason {
    REPORT_NONE = 0,
    REPORT_FIRST_READING = 1,
    REPORT_TDS_THRESHOLD = 2,
    REPORT_TEMP_THRESHOLD = 4,
    REPORT_DAILY = 8,
    REPORT_ERROR = 16,
    REPORT_ALWAYS_CONFIG = 32
};

struct ReportDecision {
    bool shouldReport;
    uint8_t reasons; // Bitfield of ReportReason values
    String reasonText;
    
    ReportDecision() : shouldReport(false), reasons(REPORT_NONE) {}
};

class ReportingLogic {
public:
    ReportingLogic(SensorDataManager& dataManager, ConnectivityManager& connectivity);
    
    // Initialize reporting logic
    void begin();
    
    // Analyze current reading and determine if reporting is needed
    ReportDecision analyzeReading(const SensorReading& currentReading);
    
    // Execute the reporting decision
    bool executeReport(const SensorReading& reading, const ReportDecision& decision);
    
    // Check if daily report is due
    bool isDailyReportDue();
    
    // Force a report (for testing or manual triggers)
    bool forceReport(const SensorReading& reading, const char* reason = "manual");
    
    // Get reporting statistics
    void printReportingStats();

private:
    SensorDataManager& dataManager;
    ConnectivityManager& connectivity;
    
    bool initialized;
    uint32_t totalReports;
    uint32_t thresholdReports;
    uint32_t dailyReports;
    uint32_t errorReports;
    
    // Helper methods
    String buildReasonText(uint8_t reasons);
    bool attemptConnection();
    void logReportAttempt(const ReportDecision& decision, bool success);
};

#endif // REPORTING_LOGIC_H
