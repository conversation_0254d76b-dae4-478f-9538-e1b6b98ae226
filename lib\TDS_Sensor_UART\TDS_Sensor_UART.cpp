#include "TDS_Sensor_UART.h"

TDS_Sensor_UART::TDS_Sensor_UART(HardwareSerial& serial) {
    _serial = &serial;
}

void TDS_Sensor_UART::begin(long baudrate, uint32_t config, int8_t rxPin, int8_t txPin) {
    _serial->begin(baudrate, config, rxPin, txPin);
    // 清空可能存在的串口接收缓冲区
    while(_serial->available()) {
        _serial->read();
    }
}

uint8_t TDS_Sensor_UART::_calculate_checksum(const uint8_t* data, size_t len) {
    uint16_t sum = 0;
    for (size_t i = 0; i < len; ++i) {
        sum += data[i];
    }
    return sum & 0xFF;
}

void TDS_Sensor_UART::_send_command(uint8_t command, uint32_t params) {
    uint8_t frame[6];
    frame[0] = command;
    // 将 32 位参数按大端模式（MSB first）打包
    frame[1] = (params >> 24) & 0xFF;
    frame[2] = (params >> 16) & 0xFF;
    frame[3] = (params >> 8) & 0xFF;
    frame[4] = params & 0xFF;
    
    frame[5] = _calculate_checksum(frame, 5);
    
    _serial->write(frame, 6);
}

bool TDS_Sensor_UART::_read_response(uint8_t* buffer, size_t len, uint16_t timeout_ms) {
    unsigned long startTime = millis();
    while (millis() - startTime < timeout_ms) {
        if (_serial->available() >= len) {
            size_t bytesRead = _serial->readBytes(buffer, len);
            if (bytesRead == len) {
                uint8_t calculated_checksum = _calculate_checksum(buffer, len - 1);
                if (buffer[len - 1] == calculated_checksum) {
                    return true; // 校验和正确
                }
            }
            return false; // 读取字节数不足或校验和错误
        }
    }
    return false; // 超时
}

TDS_Sensor_UART::TDS_Data TDS_Sensor_UART::read_tds_and_temp() {
    TDS_Data result; // 默认 valid = false
    uint8_t response[6];

    _send_command(CMD_DETECT);

    if (_read_response(response, 6) && response[0] == RESPONSE_HEADER_MEASURE) {
        // 解析 TDS (字节 1, 2)
        result.tds = (response[1] << 8) | response[2];
        // 解析温度 (字节 3, 4)，并根据手册除以 100
        result.temp = ((response[3] << 8) | response[4]) / 100.0f;
        result.valid = true;
    }
    return result;
}

uint8_t TDS_Sensor_UART::_execute_and_get_ack(uint8_t command, uint32_t params) {
    uint8_t response[6];
    _send_command(command, params);

    if (_read_response(response, 6) && response[0] == RESPONSE_HEADER_ACK) {
        return response[1]; // 返回状态码
    }
    return 0xFF; // 返回 0xFF 表示通信失败或超时
}

uint8_t TDS_Sensor_UART::calibrate() {
    return _execute_and_get_ack(CMD_CALIBRATE);
}

uint8_t TDS_Sensor_UART::set_ntc_resistance(uint32_t resistance_ohms) {
    return _execute_and_get_ack(CMD_SET_NTC_R, resistance_ohms);
}

uint8_t TDS_Sensor_UART::set_ntc_b_value(uint32_t b_value) {
    return _execute_and_get_ack(CMD_SET_NTC_B, b_value);
}

const char* TDS_Sensor_UART::getStatusMessage(uint8_t statusCode) {
    switch (statusCode) {
        case STATUS_OK:         return "Command OK";
        case STATUS_ERROR:      return "Command Error";
        case STATUS_CAL_FAILED: return "Calibration Failed";
        case STATUS_TEMP_OOR:   return "Temperature out of range";
        case 0xFF:              return "Communication Failed / Timeout";
        default:                return "Unknown Status";
    }
}